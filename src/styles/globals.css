@tailwind base;
@tailwind components;
@tailwind utilities;

/* Safe area support for mobile devices */
@supports (padding: max(0px)) {
  .pb-safe {
    padding-bottom: max(1.25rem, env(safe-area-inset-bottom));
  }
  
  .pt-safe {
    padding-top: max(1.25rem, env(safe-area-inset-top));
  }
  
  .pl-safe {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .pr-safe {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Improved touch responsiveness */
@media (hover: none) and (pointer: coarse) {
  /* Ensure minimum touch target size */
  button, 
  a,
  [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Remove hover effects on touch devices */
  .hover\:bg-gray-800\/50:hover {
    background-color: transparent;
  }
}

/* Smooth transitions for mobile sidebar */
.sidebar-mobile-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
} 