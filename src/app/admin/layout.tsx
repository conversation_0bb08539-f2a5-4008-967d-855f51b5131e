import { redirect } from 'next/navigation';
import { auth } from '@/auth';
import AdminLayoutClient from './admin-layout-client';

export default async function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();
  
  // Check if user is authenticated
  if (!session) {
    redirect('/api/auth/signin?callbackUrl=/admin');
  }
  
  // Check if user has admin access
  const hasAdminAccess = 
    session.user?.isAdmin === true || 
    session.user?.plan === 'ENTERPRISE';
  
  if (!hasAdminAccess) {
    redirect('/');
  }
  
  return <AdminLayoutClient>{children}</AdminLayoutClient>;
}