@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Enhanced Typography & Markdown Styles */
@layer components {
  /* Custom bullet points using ::marker */
  .prose ul > li::marker {
    color: rgb(99 102 241); /* indigo-500 */
    content: "●";
    font-size: 1.1em;
  }

  .prose ul ul > li::marker {
    color: rgb(34 197 94); /* green-500 */
    content: "→";
    font-size: 1em;
  }

  .prose ul ul ul > li::marker {
    color: rgb(168 85 247); /* purple-500 */
    content: "◦";
    font-size: 1.1em;
  }

  .prose ol > li::marker {
    color: rgb(99 102 241); /* indigo-500 */
    font-weight: 600;
  }

  /* Enhanced code block styling */
  .prose pre {
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
    border-radius: 0 !important;
    overflow: visible !important;
  }

  .prose pre code {
    background: transparent !important;
    padding: 0 !important;
    border-radius: 0 !important;
    font-size: inherit !important;
    color: inherit !important;
  }

  /* Enhanced table styling */
  .prose table {
    border-collapse: collapse;
    width: 100%;
    margin: 1.5rem 0;
    border: 1px solid rgb(55 65 81);
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .prose th {
    background: rgb(31 41 55);
    color: rgb(243 244 246);
    font-weight: 600;
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid rgb(55 65 81);
  }

  .prose td {
    padding: 0.75rem;
    border-bottom: 1px solid rgb(55 65 81);
    color: rgb(229 231 235);
  }

  .prose tbody tr:last-child td {
    border-bottom: none;
  }

  .prose tbody tr:nth-child(even) {
    background: rgb(17 24 39);
  }

  /* Enhanced blockquote styling */
  .prose blockquote {
    border-left: 4px solid rgb(99 102 241);
    background: rgb(31 41 55);
    padding: 1rem;
    margin: 1.5rem 0;
    border-radius: 0.5rem;
    font-style: italic;
    color: rgb(209 213 219);
  }

  .prose blockquote p {
    margin: 0;
  }

  /* Enhanced link styling */
  .prose a {
    color: rgb(99 102 241);
    text-decoration: underline;
    text-decoration-color: rgb(99 102 241 / 0.5);
    transition: all 0.2s ease;
  }

  .prose a:hover {
    color: rgb(129 140 248);
    text-decoration-color: rgb(129 140 248);
  }

  /* Enhanced heading styling */
  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: rgb(243 244 246);
    font-weight: 600;
    line-height: 1.25;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  /* Larger heading sizes for better hierarchy */
  .prose h1 {
    font-size: 1.75rem; /* 28px */
  }

  .prose h2 {
    font-size: 1.5rem; /* 24px */
  }

  .prose h3 {
    font-size: 1.375rem; /* 22px */
  }

  .prose h4 {
    font-size: 1.25rem; /* 20px */
  }

  .prose h5 {
    font-size: 1.125rem; /* 18px */
  }

  .prose h6 {
    font-size: 1rem; /* 16px */
  }

  .prose h1:first-child, .prose h2:first-child, .prose h3:first-child {
    margin-top: 0;
  }

  /* Enhanced horizontal rule */
  .prose hr {
    border: none;
    border-top: 1px solid rgb(55 65 81);
    margin: 2rem 0;
  }

  /* Enhanced paragraph spacing */
  .prose p {
    margin-top: 1rem;
    margin-bottom: 1rem;
    line-height: 1.7;
    color: rgb(229 231 235);
  }

  .prose p:first-child {
    margin-top: 0;
  }

  .prose p:last-child {
    margin-bottom: 0;
  }

  /* Enhanced list spacing */
  .prose ul, .prose ol {
    margin-top: 1rem;
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  .prose li {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
    line-height: 1.6;
    color: rgb(229 231 235);
  }

  .prose li p {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
  }

  /* Enhanced inline code styling */
  .prose :not(pre) > code {
    background: rgb(31 41 55);
    color: rgb(243 244 246);
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
    font-weight: 400;
  }

  /* Responsive typography */
  @media (max-width: 640px) {
    .prose {
      font-size: 0.875rem;
    }

    .prose h1 {
      font-size: 1.5rem; /* 24px on mobile */
    }

    .prose h2 {
      font-size: 1.375rem; /* 22px on mobile */
    }

    .prose h3 {
      font-size: 1.25rem; /* 20px on mobile */
    }

    .prose h4 {
      font-size: 1.125rem; /* 18px on mobile */
    }

    .prose h5 {
      font-size: 1rem; /* 16px on mobile */
    }

    .prose h6 {
      font-size: 0.875rem; /* 14px on mobile */
    }
  }
}

/* Highlight.js theme customization */
@layer base {
  .hljs {
    color: rgb(229 231 235) !important;
    background: rgb(17 24 39) !important;
  }

  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-literal,
  .hljs-section,
  .hljs-link {
    color: rgb(147 51 234) !important; /* purple-600 */
  }

  .hljs-function .hljs-keyword {
    color: rgb(147 51 234) !important; /* purple-600 */
  }

  .hljs-subst {
    color: rgb(229 231 235) !important;
  }

  .hljs-string,
  .hljs-title,
  .hljs-name,
  .hljs-type,
  .hljs-attribute,
  .hljs-symbol,
  .hljs-bullet,
  .hljs-addition,
  .hljs-variable,
  .hljs-template-tag,
  .hljs-template-variable {
    color: rgb(34 197 94) !important; /* green-500 */
  }

  .hljs-comment,
  .hljs-quote,
  .hljs-deletion,
  .hljs-meta {
    color: rgb(107 114 128) !important; /* gray-500 */
  }

  .hljs-number,
  .hljs-regexp,
  .hljs-literal {
    color: rgb(59 130 246) !important; /* blue-500 */
  }

  .hljs-built_in,
  .hljs-builtin-name,
  .hljs-class .hljs-title {
    color: rgb(245 158 11) !important; /* amber-500 */
  }

  .hljs-attr,
  .hljs-property,
  .hljs-title.class_,
  .hljs-title.function_ {
    color: rgb(99 102 241) !important; /* indigo-500 */
  }

  .hljs-tag {
    color: rgb(229 231 235) !important;
  }

  .hljs-emphasis {
    font-style: italic;
  }

  .hljs-strong {
    font-weight: 700;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

html {
  color-scheme: dark;
}

html.dark {
  color-scheme: dark;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background-color: hsl(var(--muted-foreground) / 0.3);
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--muted-foreground) / 0.5);
}

/* Animation utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

/* Shimmer effect for loading bar */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

/* Float effect for particles */
@keyframes float {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-10px) scale(1.1);
    opacity: 0.8;
  }
}

/* Ultra smooth thinking animation */
@keyframes ultra-think {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(1.1);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* Reduce assistant message spacing */
.assistant-message {
  font-size: 0.875rem; /* 14px */
  line-height: 1.5;
  padding: 0.5rem 0.75rem;
}

/* Even smaller padding on mobile */
@media (max-width: 640px) {
  .assistant-message {
    padding: 0.375rem 0.5rem;
  }
}

.assistant-message p {
  margin-bottom: 0.5rem;
}

.assistant-message p:last-child {
  margin-bottom: 0;
}

/* Ultra-smooth streaming optimizations */
@layer utilities {
  /* GPU acceleration for smooth animations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Smooth cursor blink */
  @keyframes cursor-blink {
    0%, 50% {
      opacity: 1;
    }
    50.01%, 100% {
      opacity: 0;
    }
  }

  /* Character appear animation */
  @keyframes char-appear {
    from {
      opacity: 0;
      transform: translateY(2px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Smooth scroll behavior */
  .smooth-scroll {
    scroll-behavior: smooth;
    /* Enable scroll anchoring to prevent jumping when content is added */
    overflow-anchor: auto;
  }

  /* Disable scroll anchoring only for specific elements that need it */
  .no-scroll-anchor {
    overflow-anchor: none;
  }

  /* Container optimizations */
  .streaming-container {
    /* Remove aggressive optimizations that cause flickering */
    /* contain: layout style paint; */
    /* content-visibility: auto; */

    /* Use minimal containment to prevent layout shifts */
    contain: style;

    /* Ensure stable rendering */
    will-change: contents;
    transform: translateZ(0);
  }

  /* Text rendering optimizations */
  .optimize-text {
    /* Use optimizeLegibility for smooth character rendering */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    /* Prevent text selection issues during streaming */
    -webkit-user-select: text;
    user-select: text;
  }

  /* Prevent layout shift */
  .stable-layout {
    contain: layout;
    isolation: isolate;
  }

  /* Ultra-smooth streaming optimizations */
  .assistant-message {
    /* Prevent font loading flicker */
    font-synthesis: none;
    -webkit-font-smoothing: subpixel-antialiased;
    -moz-osx-font-smoothing: auto;

    /* Stable rendering */
    backface-visibility: hidden;
    perspective: 1000px;

    /* Prevent selection during streaming */
    &.streaming {
      user-select: none;
    }
  }

  /* Smooth cursor animation */
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* Prevent Markdown content shifts */
  .prose {
    & > *:first-child {
      margin-top: 0 !important;
    }

    & > *:last-child {
      margin-bottom: 0 !important;
    }

    /* Stable code block rendering */
    pre {
      contain: layout style;
      overflow: hidden;

      code {
        display: block;
        overflow-x: auto;
      }
    }
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.147 0.004 49.25);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.147 0.004 49.25);
  --primary: oklch(0.216 0.006 56.043);
  --primary-foreground: oklch(0.985 0.001 106.423);
  --secondary: oklch(0.97 0.001 106.424);
  --secondary-foreground: oklch(0.216 0.006 56.043);
  --muted: oklch(0.97 0.001 106.424);
  --muted-foreground: oklch(0.553 0.013 58.071);
  --accent: oklch(0.97 0.001 106.424);
  --accent-foreground: oklch(0.216 0.006 56.043);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.923 0.003 48.717);
  --input: oklch(0.923 0.003 48.717);
  --ring: oklch(0.709 0.01 56.259);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0.001 106.423);
  --sidebar-foreground: oklch(0.147 0.004 49.25);
  --sidebar-primary: oklch(0.216 0.006 56.043);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.97 0.001 106.424);
  --sidebar-accent-foreground: oklch(0.216 0.006 56.043);
  --sidebar-border: oklch(0.923 0.003 48.717);
  --sidebar-ring: oklch(0.709 0.01 56.259);
  --background: oklch(1 0 0);
  --foreground: oklch(0.147 0.004 49.25);
}

.dark {
  --background: oklch(0.147 0.004 49.25);
  --foreground: oklch(0.985 0.001 106.423);
  --card: oklch(0.216 0.006 56.043);
  --card-foreground: oklch(0.985 0.001 106.423);
  --popover: oklch(0.216 0.006 56.043);
  --popover-foreground: oklch(0.985 0.001 106.423);
  --primary: oklch(0.923 0.003 48.717);
  --primary-foreground: oklch(0.216 0.006 56.043);
  --secondary: oklch(0.268 0.007 34.298);
  --secondary-foreground: oklch(0.985 0.001 106.423);
  --muted: oklch(0.268 0.007 34.298);
  --muted-foreground: oklch(0.709 0.01 56.259);
  --accent: oklch(0.268 0.007 34.298);
  --accent-foreground: oklch(0.985 0.001 106.423);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.553 0.013 58.071);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.216 0.006 56.043);
  --sidebar-foreground: oklch(0.985 0.001 106.423);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0.001 106.423);
  --sidebar-accent: oklch(0.268 0.007 34.298);
  --sidebar-accent-foreground: oklch(0.985 0.001 106.423);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.553 0.013 58.071);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Mobile viewport adjustments to prevent hydration mismatches */
@media (max-width: 639px) {
  /* Reduce header height on mobile */
  header > div > div {
    height: 3.5rem; /* 56px instead of 64px */
  }

  /* Adjust main content positioning on mobile */
  main.fixed {
    top: 3.5rem;
  }

  /* Reduce welcome screen padding on mobile */
  .welcome-screen-padding {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  /* Fix NewConversationScreen positioning on mobile */
  .new-conversation-screen {
    padding-top: 0.5rem;
    padding-bottom: 1rem;
    min-height: 100%;
    overflow-y: auto;
  }

  /* Ensure content isn't cut off */
  .new-conversation-screen > div {
    margin-top: auto;
    margin-bottom: auto;
  }

  /* Adjust icon size on mobile for NewConversationScreen */
  .mobile-icon-adjust {
    width: 3rem;
    height: 3rem;
  }

  .mobile-icon-adjust svg {
    width: 1.5rem;
    height: 1.5rem;
  }

  /* Adjust text size on mobile */
  .mobile-text-adjust {
    font-size: 1.25rem;
  }
}

/* Removed unused ULTRA THINK animations - now using compact design */