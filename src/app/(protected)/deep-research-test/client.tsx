/**
 * Deep Research Client - AI SDK v5 Implementation
 * 
 * ================================================================================
 * AI SDK v5 MIGRATION GUIDE & KEY DIFFERENCES FROM v4
 * ================================================================================
 * 
 * This component demonstrates the proper implementation of AI SDK v5 (v5.0.0-canary.24)
 * with comprehensive documentation of all differences, workarounds, and best practices.
 * 
 * @version AI SDK v5.0.0-canary.24, @ai-sdk/react v2.0.0-beta.28
 * @critical BETA VERSION - Contains known bugs and missing features
 * @fixed 2025-01-30 - Resolved critical message.parts undefined error
 * 
 * ================================================================================
 * 1. MESSAGE FORMAT CHANGES (CRITICAL FOR v5 BETA)
 * ================================================================================
 * 
 * 🚨 CRITICAL: AI SDK v5 beta requires ALL messages to have a 'parts' property!
 * Missing this causes: "TypeError: can't access property 'filter', message.parts is undefined"
 * 
 * v4 Message Structure (BREAKS in v5):
 * ```typescript
 * {
 *   id: string,
 *   role: 'user' | 'assistant',
 *   content: string  // Simple string content - NOT ENOUGH for v5!
 * }
 * ```
 * 
 * v5 UIMessage Structure (REQUIRED):
 * ```typescript
 * {
 *   id: string,
 *   role: 'user' | 'assistant' | 'system' | 'data',
 *   content: string,  // Keep for compatibility
 *   parts: Array<{    // REQUIRED: All content now in parts array
 *     type: 'text' | 'tool-invocation' | 'reasoning' | 'source' | 'step-start',
 *     text?: string,
 *     toolInvocation?: { // Beta format - different from final v5!
 *       toolName: string,
 *       args: any,
 *       state: string,
 *       result?: any
 *     }
 *   }>,
 *   createdAt?: Date,
 *   annotations?: Array<JSONValue>
 * }
 * ```
 * 
 * ✅ CORRECT MESSAGE CREATION:
 * ```typescript
 * const message = {
 *   role: 'user',
 *   content: 'Hello world',
 *   parts: [{ type: 'text', text: 'Hello world' }]  // REQUIRED!
 * }
 * ```
 * 
 * ⚠️ IMPORTANT: Always render message.parts, not message.content!
 * 
 * ================================================================================
 * 2. TOOL INVOCATION FORMAT (BETA BUG)
 * ================================================================================
 * 
 * v5 Final (per docs): part.type === 'tool-call'
 * v5 Beta (our version): part.type === 'tool-invocation' with toolInvocation object
 * 
 * ```typescript
 * // Beta format we must use:
 * if (part.type === 'tool-invocation') {
 *   console.log(part.toolInvocation.toolName)  // NOT part.toolName!
 * }
 * ```
 * 
 * ================================================================================
 * 3. API ENDPOINT CONFIGURATION (CRITICAL BUG)
 * ================================================================================
 * 
 * 🐛 KNOWN BUG: The `api` prop is cached at initialization and ignored!
 * GitHub Issues: #7070, #7109
 * 
 * ❌ DOESN'T WORK:
 * ```typescript
 * const { handleSubmit } = useChat({
 *   api: '/api/chat/custom-endpoint'  // This is ignored!
 * })
 * handleSubmit(e)  // Will call /api/chat instead
 * ```
 * 
 * ✅ WORKAROUND 1 - Use append with API override:
 * ```typescript
 * append({ role: 'user', content: text }, {
 *   api: '/api/chat/custom-endpoint'  // Override at send time
 * })
 * ```
 * 
 * ✅ WORKAROUND 2 - Custom form handler:
 * ```typescript
 * const onSubmit = (e) => {
 *   e.preventDefault()
 *   if (input && input.trim()) {
 *     handleSubmit(e)  // Only if using default /api/chat
 *   }
 * }
 * ```
 * 
 * ================================================================================
 * 4. TRANSPORT SYSTEM (MISSING IN BETA)
 * ================================================================================
 * 
 * v5 Final requires DefaultChatTransport:
 * ```typescript
 * import { DefaultChatTransport } from 'ai'
 * useChat({
 *   transport: new DefaultChatTransport({ api: '/api/chat' })
 * })
 * ```
 * 
 * ⚠️ BUT: DefaultChatTransport doesn't exist in beta v5.0.0-canary.24!
 * Must use direct api prop instead (with workarounds above)
 * 
 * ================================================================================
 * 5. REMOVED/CHANGED FEATURES
 * ================================================================================
 * 
 * ❌ REMOVED:
 * - sendExtraMessageFields (now default behavior)
 * - streamMode (use streamProtocol: 'text' | 'data')
 * - keepLastMessageOnError (no longer needed)
 * - options parameter (use headers/body directly)
 * - isLoading helper (use status === 'streaming' || status === 'submitted')
 * - append() function (use sendMessage() in final v5)
 * 
 * 🔄 CHANGED:
 * - experimental_resume → resumeStream
 * - append() still exists in beta but requires API override
 * - handleSubmit requires custom wrapper for validation
 * 
 * ================================================================================
 * 6. SERVER-SIDE REQUIREMENTS
 * ================================================================================
 * 
 * Always use convertToModelMessages:
 * ```typescript
 * import { convertToModelMessages, streamText } from 'ai'
 * 
 * const result = streamText({
 *   model: openai('gpt-4o'),
 *   messages: convertToModelMessages(messages), // Required!
 * })
 * return result.toUIMessageStreamResponse()
 * ```
 * 
 * ================================================================================
 * 7. BEST PRACTICES FOR v5 BETA
 * ================================================================================
 * 
 * 1. Always check if features exist before using
 * 2. Use append() with API override for custom endpoints
 * 3. Render message.parts, not message.content
 * 4. Handle tool-invocation (beta) not tool-call (final)
 * 5. Add onError handlers to all useChat hooks
 * 6. Use status instead of isLoading
 * 7. Test thoroughly - beta has many edge cases
 * 
 * ================================================================================
 * 8. COMMON PITFALLS
 * ================================================================================
 * 
 * ❌ DON'T: Expect DefaultChatTransport to exist
 * ❌ DON'T: Use handleSubmit directly without validation
 * ❌ DON'T: Render message.content (use parts)
 * ❌ DON'T: Expect api prop to work without workarounds
 * ❌ DON'T: Use tool-call part type (use tool-invocation)
 * 
 * ================================================================================
 */
'use client'

// React hooks for state management and lifecycle
import { useState, useEffect, useMemo } from 'react'

// AI SDK v5 imports - using beta-compatible direct API approach
import { useChat } from '@ai-sdk/react' // v5: useChat from @ai-sdk/react package
// Note: DefaultChatTransport is NOT available in v5.0.0-canary.24 beta

// UI component imports
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Brain, Send, Loader2, Info } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ResearchSidebar } from '@/components/deep-research/research-sidebar'

// Deep research type definitions
import type { 
  DeepResearchToolResponse
} from '@/types/deep-research'

/**
 * Props interface for the Deep Research Test Client
 * @param conversationId - Optional conversation ID for message persistence
 * @param initialMessages - Optional array of initial messages to load
 */
interface DeepResearchTestPageProps {
  conversationId?: string
  initialMessages?: any[] // Using any[] for compatibility with AI SDK v5 beta
}

export default function DeepResearchTestClient({ conversationId: providedId, initialMessages }: DeepResearchTestPageProps = {}) {
  const [researchSessionId, setResearchSessionId] = useState<string | null>(null)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  
  // v5: Generate a stable conversation ID for message persistence
  const conversationId = useMemo(() => {
    // Use provided ID or generate new one
    return providedId || `deep-research-${Date.now()}-${Math.random().toString(36).substring(7)}`
  }, [providedId])
  
  // Log conversation details
  useEffect(() => {
    console.log('[DeepResearch Client] Initialized with:', {
      conversationId,
      providedId,
      initialMessageCount: initialMessages?.length || 0
    })
  }, [conversationId, providedId, initialMessages])

  // v5: useChat hook with comprehensive configuration
  console.log('[DeepResearch] 🔧 Initializing useChat hook with config:', {
    conversationId,
    api: '/api/chat', // Default endpoint - will use deepResearch flag in body
    initialMessagesCount: initialMessages?.length || 0,
    timestamp: new Date().toISOString()
  })
  
  const {
    messages,          // v5: messages array in UIMessage format
    input,             // v5: current input value
    setInput,          // v5: set input function
    handleInputChange, // v5: input change handler
    handleSubmit,      // v5: form submit handler (has known bugs)
    status,            // v5: 'ready' | 'submitted' | 'streaming' | 'error'
    error,             // v5: error state
    append,            // v5: append function for sending messages
    setMessages        // v5: function to modify messages
  } = useChat({
    id: conversationId, // v5: Pass conversation ID for persistence
    // NOTE: Using default /api/chat endpoint with deepResearch flag in body
    onError: (error) => {
      console.error('[DeepResearch] 🚨 useChat onError callback:', {
        error,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        errorStack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString()
      })
    },
    onFinish: ({ message }) => {
      console.log('[DeepResearch] ✅ useChat onFinish callback:', {
        message,
        messageId: message.id,
        messageRole: message.role,
        partsCount: message.parts?.length || 0,
        timestamp: new Date().toISOString()
      })
      
      // v5: Handle tool invocation responses - check for research session start or status updates
      if (message.parts && Array.isArray(message.parts)) {
        message.parts.forEach((part) => {
          // v5: Check for tool-invocation parts (beta still uses generic format)
          if (part.type === 'tool-invocation' && part.toolInvocation) {
            const toolInvocationWithResult = part.toolInvocation as any
            
            if (part.toolInvocation.toolName === 'deepResearch') {
              const result = toolInvocationWithResult.result as DeepResearchToolResponse
              if (result?.type === 'research_started') {
                setResearchSessionId(result.sessionId)
                setSidebarOpen(true) // Open sidebar when research starts
              }
            }
          }
        })
      }
    }
  })
  
  // v5: Compute isLoading from status (isLoading helper removed in v5)
  const isLoading = status === 'streaming' || status === 'submitted'
  
  // Log useChat hook initialization results
  console.log('[DeepResearch] ✅ useChat hook initialized:', {
    messagesCount: messages?.length || 0,
    status,
    isLoading,
    error: error ? { message: error.message, type: error.constructor.name } : null,
    functions: {
      append: typeof append,
      handleSubmit: typeof handleSubmit,
      handleInputChange: typeof handleInputChange,
      setMessages: typeof setMessages
    },
    appendExists: !!append,
    appendIsFunction: typeof append === 'function',
    timestamp: new Date().toISOString()
  })

  // Debug useChat hook state with comprehensive logging
  useEffect(() => {
    console.log('[DeepResearch] 📊 useChat state update:', {
      messagesCount: messages?.length || 0,
      status,
      isLoading,
      error: error ? { message: error.message, type: error.constructor.name } : null,
      functions: {
        append: typeof append,
        handleSubmit: typeof handleSubmit,
        handleInputChange: typeof handleInputChange,
        setMessages: typeof setMessages
      },
      appendExists: !!append,
      appendIsFunction: typeof append === 'function',
      conversationId,
      timestamp: new Date().toISOString()
    })
    
    // Critical check: Ensure append function is available
    if (typeof append !== 'function') {
      console.error('[DeepResearch] 🚨 CRITICAL: append function is not available!', {
        appendType: typeof append,
        appendValue: append,
        allFunctions: {
          append: typeof append,
          handleSubmit: typeof handleSubmit,
          handleInputChange: typeof handleInputChange,
          setMessages: typeof setMessages
        }
      })
    } else {
      console.log('[DeepResearch] ✅ append function is properly available')
    }
  }, [messages, status, isLoading, append, handleSubmit, handleInputChange, setMessages, error, conversationId])

  // Debug status changes
  useEffect(() => {
    console.log('Status changed to:', status)
  }, [status])

  // Debug messages
  useEffect(() => {
    console.log('Messages updated:', messages.length, messages)
  }, [messages])


  /**
   * Main component render function
   * 
   * @returns {JSX.Element} The complete deep research test interface
   * 
   * @description
   * Renders the full deep research testing interface with:
   * 1. Header card with title and conversation ID
   * 2. Info alert explaining how the feature works
   * 3. Message history with AI SDK v5 parts rendering
   * 4. Research session status indicator
   * 5. Loading spinner for streaming states
   * 6. Input form with submit button
   * 
   * The interface adapts to different states:
   * - Normal chat mode: Shows input form and message history
   * - Streaming mode: Shows loading spinner, disables input
   * - Research active: Shows session ID and status
   * - Error mode: Shows error in message, enables retry
   */
  return (
    <div className="container mx-auto max-w-4xl p-4">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="w-6 h-6" />
            Deep Research Test (Tool Implementation)
          </CardTitle>
          <CardDescription>
            Test the deep research feature with AI SDK v5 tool calls
            <br />
            <span className="text-xs text-muted-foreground">Conversation ID: {conversationId}</span>
          </CardDescription>
        </CardHeader>
      </Card>

      <Alert className="mb-6">
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>How it works:</strong> Ask a research question → The AI may ask clarifying questions → Answer them conversationally → Get comprehensive research results.
          <br />
          <strong>Status:</strong> {status}
        </AlertDescription>
      </Alert>

      <div className="space-y-4">
        {/* Messages - AI SDK v5 format with parts array rendering */}
        {messages.map((message, index) => (
          <Card key={index} className={message.role === 'user' ? 'ml-auto max-w-[80%]' : 'mr-auto max-w-[80%]'}>
            <CardContent className="pt-6">
              <div className="flex items-start gap-2">
                <Badge variant={message.role === 'user' ? 'default' : 'secondary'}>
                  {message.role}
                </Badge>
                <div className="flex-1 prose prose-sm dark:prose-invert max-w-none">
                  {/* v5: Render message parts array (text, tool-invocation, etc) */}
                  {message.parts && message.parts.map((part, partIdx) => {
                    // v5: Handle different part types in the parts array
                    switch (part.type) {
                      case 'text':
                        // v5: text parts have 'text' property
                        return (
                          <p key={`text-${partIdx}`} className="whitespace-pre-wrap">
                            {part.text}
                          </p>
                        );
                      
                      case 'tool-invocation':
                        // v5: Tool invocations in beta still use generic format with toolInvocation object
                        return (
                          <div key={`tool-${partIdx}`} className="mt-2 p-3 bg-muted rounded-lg">
                            <p className="text-xs font-semibold mb-1">Tool Execution:</p>
                            <div className="text-xs space-y-1">
                              <div className="flex items-center gap-2">
                                <span className="font-mono bg-primary/10 px-2 py-0.5 rounded">
                                  {part.toolInvocation?.toolName || 'unknown'} {/* v5: toolName in toolInvocation object */}
                                </span>
                                <span className="text-muted-foreground">
                                  {part.toolInvocation?.state || 'completed'} {/* v5: state in toolInvocation object */}
                                </span>
                              </div>
                              {part.toolInvocation?.args && (
                                <div className="pl-4">
                                  <span className="text-muted-foreground">Args:</span>
                                  <pre className="mt-1 text-xs overflow-x-auto bg-background p-2 rounded">
                                    {JSON.stringify(part.toolInvocation.args, null, 2)} {/* v5: args in toolInvocation */}
                                  </pre>
                                </div>
                              )}
                              {(part.toolInvocation as any)?.result && (
                                <div className="pl-4">
                                  <span className="text-muted-foreground">Result:</span>
                                  <pre className="mt-1 text-xs overflow-x-auto bg-background p-2 rounded">
                                    {JSON.stringify((part.toolInvocation as any).result, null, 2)} {/* v5: result in toolInvocation */}
                                  </pre>
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      
                      default:
                        // Unknown part type - return null
                        return null;
                    }
                  })}
                  
                  {/* v5: Fallback for older format (content field) during migration */}
                  {!message.parts && (message as any).content && (
                    <p className="whitespace-pre-wrap">{(message as any).content}</p>
                  )}
                  
                  {/* Show placeholder if no parts and no content */}
                  {(!message.parts || message.parts.length === 0) && !(message as any).content && (
                    <p className="text-muted-foreground italic">Processing...</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}


        {/* Research Status Indicator */}
        {researchSessionId && !sidebarOpen && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Brain className="w-4 h-4 text-primary" />
                  <span className="text-sm">Deep research session active</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSidebarOpen(true)}
                >
                  View Progress
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* v5: Show loading spinner when AI is processing */}
        {isLoading && !messages.some(m => m.role === 'assistant' && status === 'streaming') && (
          <div className="flex justify-center">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        )}

        {/* Input form - v5 beta compatibility with useChat hook */}
        {/* Form is disabled during follow-up questions or when AI is processing */}
        <form onSubmit={(e) => {
          console.log('[DeepResearch] 🚀 FORM SUBMIT EVENT TRIGGERED')
          console.log('[DeepResearch] Event details:', {
            type: e.type,
            target: e.target,
            preventDefault: typeof e.preventDefault
          })
          
          e.preventDefault()
          console.log('[DeepResearch] ✅ preventDefault() called')
          
          // Log all current state
          console.log('[DeepResearch] 📊 Current state:', {
            input: input,
            inputLength: input?.length || 0,
            inputTrimmed: input?.trim(),
            isLoading,
            status,
            appendFunction: typeof append,
            appendExists: !!append,
            conversationId
          })
          
          // Check if append function exists and is callable
          if (typeof append !== 'function') {
            console.error('[DeepResearch] ❌ CRITICAL: append is not a function!', {
              appendType: typeof append,
              appendValue: append
            })
            return
          }
          
          console.log('[DeepResearch] ✅ append function is available')
          
          if (input && input.trim() && !isLoading) {
            console.log('[DeepResearch] ✅ Form validation passed')
            console.log('[DeepResearch] 🔧 Using append workaround for API endpoint')
            
            const messageToSend = {
              role: 'user' as const,
              content: input,
              parts: [{
                type: 'text' as const,
                text: input
              }]
            }
            
            // Note: API override not supported in this beta version
            // Will implement manual fetch to custom endpoint
            
            console.log('[DeepResearch] 📤 About to call append with:', {
              message: messageToSend,
              timestamp: new Date().toISOString()
            })
            
            // Monitor network requests
            console.log('[DeepResearch] 🌐 Check browser Network tab for requests to default endpoint')
            
            try {
              console.log('[DeepResearch] 🔄 Calling append function...')
              
              // Call append with deepResearch flag in body
              console.log('[DeepResearch] 🚀 SENDING REQUEST with deepResearch: true')
              console.log('[DeepResearch] 📝 Message:', messageToSend)
              const appendResult = append(messageToSend, {
                body: {
                  deepResearch: true  // This flag triggers the redirect in the main chat route
                }
              })
              console.log('[DeepResearch] ✅ Append result:', appendResult)
              
              console.log('[DeepResearch] ✅ Append function returned:', {
                result: appendResult,
                resultType: typeof appendResult,
                isPromise: appendResult instanceof Promise
              })
              
              // If append returns a promise, log when it resolves/rejects
              if (appendResult instanceof Promise) {
                appendResult
                  .then((result) => {
                    console.log('[DeepResearch] ✅ Append promise resolved:', result)
                  })
                  .catch((error) => {
                    console.error('[DeepResearch] ❌ Append promise rejected:', error)
                  })
              }
              
              console.log('[DeepResearch] 🧹 Clearing input field')
              setInput('') // Clear input after sending
              
              console.log('[DeepResearch] ✅ Form submission completed successfully')
              
            } catch (error) {
              console.error('[DeepResearch] ❌ CRITICAL ERROR calling append:', {
                error,
                errorMessage: error instanceof Error ? error.message : 'Unknown error',
                errorStack: error instanceof Error ? error.stack : undefined,
                errorType: error instanceof Error ? error.constructor.name : typeof error
              })
            }
          } else {
            console.log('[DeepResearch] ❌ Form validation failed:', {
              hasInput: !!input,
              inputValue: input,
              inputTrimmed: input?.trim(),
              inputTrimmedLength: input?.trim()?.length || 0,
              isLoading,
              validationResults: {
                hasInputCheck: !!input,
                hasTrimmedInput: !!(input && input.trim()),
                notLoading: !isLoading
              }
            })
          }
          
          console.log('[DeepResearch] 🏁 Form submit handler completed')
        }} className="flex gap-2">
          <Textarea
            value={input}
            onChange={handleInputChange} // v5: handleInputChange from useChat hook
            placeholder="Ask a question that requires deep research..."
            disabled={isLoading} // Disable during processing
            rows={3}
            className="flex-1 resize-none"
          />
          <Button 
            type="submit" 
            disabled={isLoading || !input.trim()}
            size="icon"
            className="h-auto"
          >
            {/* Show loading spinner during AI processing, otherwise show send icon */}
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : (
              <Send className="w-4 h-4" />
            )}
          </Button>
        </form>
      </div>

      {/* Research Sidebar */}
      <ResearchSidebar
        sessionId={researchSessionId}
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />
    </div>
  )
}