/**
 * Main Chat API Route - AI SDK v5 Modular Architecture
 * 
 * Clean, modular implementation using separated concerns
 */

import { NextRequest, NextResponse } from 'next/server'

// Set maximum duration for streaming responses
export const maxDuration = 60 // 60 seconds timeout

// Import modular components
import { authenticateRequest, RateLimitError } from '@/lib/api/chat/auth'
import { RedisRateLimitService } from '@/lib/rate-limit-redis'
import { selectModel } from '@/lib/api/chat/model/selector'
import { processWebSearch } from '@/lib/api/chat/features/web-search'
import { processImageGeneration } from '@/lib/api/chat/features/image-generation'
import {
  ensureConversation,
  logUserMessage,
  logAssistantMessage,
  trackUsage,
  generateConversationTitleInBackground
} from '@/lib/api/chat/database/operations'
import { createStreamConfig, streamChatResponse } from '@/lib/api/chat/streaming/handler'
import { apiLogger } from '@/lib/logger'
import { safeJSONStringify } from '@/lib/utils/unicode-sanitizer'

// Import master integration coordinator
import { createMasterCoordinator } from '@/lib/api/chat/integration/master-coordinator'

// Import AI SDK v5 tools
import { tool } from 'ai'
import { z } from 'zod'
import { searchManager } from '@/lib/ai/search'
import { modelSupportsTools } from '@/lib/api/chat/model/tool-support'

// V5 Attachment System
import { 
  parseV5ChatRequest,
  streamV5ChatWithAttachments,
  validateAndProcessV5Attachments,
  getV5AttachmentMetadata,
  logV5AttachmentProcessing
} from '@/lib/api/chat/attachments/v5-chat-integration'

// Additional imports for HTML Canvas tool
import { nanoid } from 'nanoid'

/**
 * Check if a model supports reasoning/thinking to avoid streaming errors
 */
function isReasoningModel(modelId: string): boolean {
  // OpenAI reasoning models (o-series)
  if (modelId.includes('o1') || modelId.includes('o3') || modelId.includes('o4')) {
    return true
  }
  
  // Google thinking models (with -thinking suffix or native support)
  if (modelId.includes('gemini') && 
      (modelId.includes('-thinking') || 
       modelId.includes('gemini-2.5-pro') || 
       modelId.includes('gemini-2.0-flash-thinking'))) {
    return true
  }
  
  // Claude thinking models (with -thinking suffix)
  if (modelId.includes('claude') && modelId.includes('-thinking')) {
    return true
  }
  
  // Groq reasoning models (QWen-QWQ, DeepSeek R1, -reasoning suffix)
  // IMPORTANT: Kimi K2 is explicitly excluded - it does NOT support reasoning UI features or reasoningFormat parameter
  if (modelId.includes('groq') && 
      (modelId.includes('qwen-qwq') || modelId.includes('deepseek-r1') || modelId.includes('-reasoning')) &&
      !modelId.includes('kimi-k2')) {
    return true
  }
  
  // xAI reasoning models (only those with -reasoning suffix)
  if (modelId.includes('xai') && modelId.includes('-reasoning')) {
    return true
  }
  
  // DeepSeek reasoning models
  if (modelId.includes('deepseek') && 
      (modelId.includes('reasoner') || modelId.includes('r1'))) {
    return true
  }
  
  return false
}

/**
 * Main POST handler for chat requests
 */
export async function POST(request: NextRequest) {
  console.log('🚀 [DEBUG] MAIN /api/chat route called!')
  const startTime = Date.now()
  
  try {
    console.log('[TOOLS DEBUG] /api/chat route handler started')
    
    // Check if this is a deep research request
    const requestClone = request.clone()
    const body = await requestClone.json()
    
    // Automatic research detection based on user message
    const lastMessage = body.messages?.[body.messages.length - 1]
    const lastMessageContent = lastMessage?.content?.toLowerCase() || ''
    
    // Research detection patterns
    const isResearchRequest = 
      body.deepResearch === true || // Explicit flag
      lastMessageContent.includes('research') ||
      lastMessageContent.includes('news') ||
      lastMessageContent.includes('latest') ||
      lastMessageContent.includes('recent') ||
      lastMessageContent.includes('analyze') ||
      lastMessageContent.includes('tell me about') ||
      lastMessageContent.includes('information on') ||
      lastMessageContent.includes('find out') ||
      lastMessageContent.includes('what happened')
    
    if (isResearchRequest) {
      console.log('[Chat] Research request detected:', {
        explicitFlag: body.deepResearch === true,
        autoDetected: body.deepResearch !== true && isResearchRequest,
        message: lastMessageContent.substring(0, 100),
        redirecting: true
      })
      // Import the deep research handler
      const { POST: deepResearchHandler } = await import('./deep-research-simple-test/route')
      // Pass the original request (not the clone) to the handler
      return deepResearchHandler(request)
    }
    
    // ============================================================================
    // 1. Authentication & Rate Limiting
    // ============================================================================
    const authResult = await authenticateRequest(request)
    const { session, isAuthenticated } = authResult
    
    console.log('[Chat] Auth completed:', { 
      authenticated: isAuthenticated,
      userId: session?.user?.id,
      elapsed: `${Date.now() - startTime}ms`
    })
    
    // ============================================================================
    // 2. Parse & Validate Request with V5 Attachment Support
    // ============================================================================
    const chatRequest = await parseV5ChatRequest(request)
    
    // Get V5 attachment metadata for logging
    const attachmentMetadata = getV5AttachmentMetadata(chatRequest)
    
    console.log('[Chat] V5 Request parsed:', {
      messages: chatRequest.messages.length,
      conversationId: chatRequest.conversationId,
      model: chatRequest.manualModel || 'auto',
      webSearch: chatRequest.webSearchEnabled,
      attachments: attachmentMetadata.attachmentCount,
      attachmentTypes: attachmentMetadata.attachmentTypes,
      attachmentSize: `${(attachmentMetadata.totalSize / 1024 / 1024).toFixed(2)}MB`,
      v5NativeSupport: attachmentMetadata.nativeSupport,
      ultraThink: chatRequest.ultraThink,
      elapsed: `${Date.now() - startTime}ms`
    })
    
    // Log V5 attachment processing details
    if (attachmentMetadata.hasAttachments) {
      logV5AttachmentProcessing(chatRequest, chatRequest.manualModel || 'auto')
    }
    
    // ============================================================================
    // 3. Model Selection (Manual or Intelligent Router) with Access Control
    // ============================================================================
    const modelSelection = await selectModel(chatRequest, session, authResult.anonymousSession)
    
    console.log('[Chat] Model selected:', {
      model: modelSelection.model,
      isManual: modelSelection.isManual,
      category: modelSelection.category,
      elapsed: `${Date.now() - startTime}ms`
    })
    
    // ============================================================================
    // 4. Process Web Search (if enabled)
    // ============================================================================
    const searchResult = await processWebSearch(
      chatRequest.messages,
      modelSelection.model,
      chatRequest.webSearchEnabled || false,
      modelSelection.analysisResult
    )
    
    if (searchResult.searchPerformed) {
      console.log('[Chat] Web search completed:', {
        originalMessages: chatRequest.messages.length,
        processedMessages: searchResult.messages.length,
        elapsed: `${Date.now() - startTime}ms`
      })
    }
    
    // ============================================================================
    // 5. Check for Image Generation Request
    // ============================================================================
    // We need conversation ID for image generation, so ensure it exists first
    const preliminaryConversationId = chatRequest.conversationId || 
      await ensureConversation(
        chatRequest.conversationId,
        session?.user?.id,
        modelSelection.model,
        searchResult.messages,
        authResult.anonymousSession
      )
    
    const imageGenResult = await processImageGeneration(
      searchResult.messages,
      preliminaryConversationId,
      session?.user?.id,
      authResult.anonymousSession?.virtualId,
      (progress) => {
        // Progress updates will be handled via streaming
        console.log('[Chat] Image generation progress:', progress)
      }
    )
    
    if (imageGenResult.isImageGeneration) {
      console.log('[Chat] Image generation processed:', {
        generated: imageGenResult.imageGenerated,
        generationId: imageGenResult.generationId,
        hasError: !!imageGenResult.error,
        elapsed: `${Date.now() - startTime}ms`
      })
      
      // If image was generated successfully and we shouldn't continue with text
      if (imageGenResult.imageGenerated && !imageGenResult.shouldContinueWithText) {
        // Return early with image response
        const encoder = new TextEncoder()
        
        return new Response(
          new ReadableStream({
            async start(controller) {
              // Send image generation event
              const imageEvent = {
                type: 'imageGeneration',
                generationId: imageGenResult.generationId,
                imageUrl: imageGenResult.imageUrl,
                originalPrompt: imageGenResult.originalPrompt,
                enhancedPrompt: imageGenResult.enhancedPrompt
              }
              
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(imageEvent)}\n\n`))
              
              // Send the formatted message content
              if (imageGenResult.injectedMessage) {
                const messageEvent = {
                  type: 'content',
                  content: imageGenResult.injectedMessage.content
                }
                controller.enqueue(encoder.encode(`data: ${JSON.stringify(messageEvent)}\n\n`))
              }
              
              // Send completion event
              const doneEvent = { type: 'done' }
              controller.enqueue(encoder.encode(`data: ${JSON.stringify(doneEvent)}\n\n`))
              
              controller.close()
            }
          }),
          {
            headers: {
              'Content-Type': 'text/event-stream',
              'Cache-Control': 'no-cache',
              'Connection': 'keep-alive',
              ...(authResult.anonymousSessionCookieHeader && {
                'Set-Cookie': authResult.anonymousSessionCookieHeader
              })
            }
          }
        )
      }
      
      // If there was an error or we should continue with text, inject error message
      if (imageGenResult.error) {
        searchResult.messages.push({
          id: `error-${Date.now()}`,
          role: 'system',
          content: `Image generation failed: ${imageGenResult.error}. I'll help you with a text response instead.`,
          createdAt: new Date()
        })
      }
    }
    
    // ============================================================================
    // 6. Integrate All Sophisticated Features via Master Coordinator
    // ============================================================================
    const coordinator = createMasterCoordinator({
      enableAdvancedModelHandling: true,
      enablePersonalization: true,
      enableConversationCompaction: true,
      enableTitleGeneration: true,
      debug: process.env.NODE_ENV === 'development'
    })
    
    // Convert search result messages to ModelMessage format for integration
    const coreMessages = searchResult.messages.map(msg => {
      if (msg.role === 'system') {
        return {
          role: 'system' as const,
          content: typeof msg.content === 'string' ? msg.content : JSON.stringify(msg.content)
        }
      } else if (msg.role === 'user') {
        return {
          role: 'user' as const,
          content: msg.content
        }
      } else {
        return {
          role: 'assistant' as const,
          content: msg.content
        }
      }
    })
    
    const integratedFeatures = await coordinator.integrateAllFeatures(
      modelSelection.model,
      coreMessages,
      chatRequest,
      session,
      modelSelection
    )
    
    console.log('[Chat] All sophisticated features integrated:', {
      features: integratedFeatures.metadata.features,
      processingTime: `${integratedFeatures.metadata.totalProcessingTime}ms`,
      modelType: integratedFeatures.modelConfiguration.modelType,
      hasPersonalization: integratedFeatures.personalization.hasPersonalization,
      compressionRatio: integratedFeatures.compactionResult.metadata.compressionRatio,
      elapsed: `${Date.now() - startTime}ms`
    })
    
    // Messages are already processed by the master coordinator
    // Convert back to ChatMessage format for database operations
    console.log('[Chat] Processing integrated features messages:', {
      hasProcessedMessages: !!integratedFeatures.processedMessages,
      processedMessagesType: typeof integratedFeatures.processedMessages,
      processedMessagesLength: integratedFeatures.processedMessages?.length || 0
    })
    
    // Preserve original message structure including attachments and parts
    const processedMessages = (integratedFeatures.processedMessages || []).map((msg, index) => {
      // Find the original message to preserve attachments/parts
      const originalMsg = chatRequest.messages.find(m => 
        m.role === msg.role && m.content === msg.content
      )
      
      return {
        id: originalMsg?.id || `msg-${Date.now()}-${index}`,
        role: msg.role,
        content: msg.content,
        createdAt: originalMsg?.createdAt || new Date(),
        // IMPORTANT: Preserve parts from original messages (v5 uses parts for attachments)
        ...(originalMsg && 'parts' in originalMsg && { parts: originalMsg.parts })
      }
    })
    
    // ============================================================================
    // 7. Ensure Conversation Exists (with anonymous session support)
    // ============================================================================
    const conversationId = preliminaryConversationId || await ensureConversation(
      chatRequest.conversationId,
      session?.user?.id,
      modelSelection.model,
      processedMessages,
      authResult.anonymousSession
    )
    
    console.log('[Chat] Conversation ready:', {
      conversationId,
      isNew: !chatRequest.conversationId,
      elapsed: `${Date.now() - startTime}ms`
    })
    
    // ============================================================================
    // 8. Log User Message (Non-blocking) - Enhanced with anonymous support
    // ============================================================================
    // Use the original message from chatRequest to preserve attachments/parts
    const lastOriginalUserMessage = chatRequest.messages.findLast(m => m.role === 'user')
    if (lastOriginalUserMessage && (session?.user || authResult.anonymousSession)) {
      logUserMessage(
        session?.user?.id,
        conversationId,
        lastOriginalUserMessage,
        modelSelection.model,
        authResult.anonymousSession
      )
      
      // Increment Redis rate limit usage for anonymous users
      if (!session?.user && authResult.rateLimitResult?.identifier) {
        RedisRateLimitService.incrementUsageWithVirtualId(
          authResult.rateLimitResult.identifier,
          authResult.anonymousSession?.virtualId || 'unknown',
          request
        ).catch(error => {
          console.error('[Chat] Failed to increment Redis rate limit:', error)
        })
      }
    }
    
    // ============================================================================
    // 9. Prepare Tool Integration - DIRECT API APPROACH
    // ============================================================================
    // Check if tools should be enabled based on request AND model support
    const shouldEnableTools = chatRequest.toolsEnabled !== false && modelSupportsTools(modelSelection.model)
    
    if (chatRequest.toolsEnabled !== false && !shouldEnableTools) {
      console.warn('[Chat] Tools requested but model does not support them:', modelSelection.model)
    }
    
    // Create simple AI SDK v5 tools with direct API calls
    const tools = shouldEnableTools ? {
      searchWeb: tool({
        description: 'Search the web for current information using Brave Search API',
        parameters: z.object({
          query: z.string().describe('The search query to find current information')
        }),
        async execute({ query }) {
          console.log('[SearchWebTool] 🔍 Executing search for:', query)
          
          try {
            // Create a timeout promise (3 minutes)
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Search timeout after 3 minutes')), 180000)
            })
            
            // Check if query implies recency
            const needsFreshContent = /\b(today|latest|recent|current|now|news|breaking)\b/i.test(query)
            
            // Race between search and timeout
            const results = await Promise.race([
              searchManager.search(query, {
                limit: 15, // Increased from 8 to get more results
                type: 'web',
                timeRange: needsFreshContent ? 'day' : 'week',
                freshness: needsFreshContent ? 'pd' : undefined, // Past day for fresh content
                extraSnippets: true // Get more context from each result
              }),
              timeoutPromise
            ]) as any
            
            console.log('[SearchWebTool] ✅ Found', results.length, 'results')
            
            if (results.length === 0) {
              return {
                success: false,
                query,
                message: 'No search results found',
                results: []
              }
            }
            
            // Format results for the model with extra context
            const formattedResults = results.map((result: any) => ({
              title: result.title,
              url: result.url,
              snippet: result.snippet,
              relevance: result.score,
              // Include extra snippets if available
              extraSnippets: result.metadata?.extraSnippets || [],
              publishedDate: result.metadata?.publishedDate,
              age: result.metadata?.age
            }))
            
            console.log('[SearchWebTool] 🚀 Returning real Brave Search results')
            
            return {
              success: true,
              query,
              resultCount: results.length,
              results: formattedResults
            }
            
          } catch (error) {
            console.error('[SearchWebTool] ❌ Search failed:', error)
            return {
              success: false,
              query,
              error: error instanceof Error ? error.message : 'Search failed',
              results: []
            }
          }
        }
      }),
      createHTMLCanvas: tool({
        description: 'Create interactive HTML/CSS/JavaScript code examples, web pages, demos, buttons, forms, games, animations, or any HTML-based content that the user requests. Use this tool when the user asks to create, build, make, or generate HTML, CSS, JavaScript, web pages, interactive elements, or code examples.',
        parameters: z.object({
          html: z.string().min(10).describe('Complete HTML content including CSS and JavaScript. Should include <!DOCTYPE html>, <html>, <head>, and <body> tags for a complete webpage.'),
          title: z.string().optional().describe('Optional title for the canvas (defaults to "HTML Canvas")')
        }),
        async execute({ html, title = 'HTML Canvas' }) {
          console.log('[HTML_CANVAS] Creating canvas:', { title, htmlLength: html.length })
          
          try {
            const artifactId = nanoid()

            // Analyze the HTML content
            const hasCSS = /<style[\s\S]*?<\/style>/i.test(html) || /style\s*=/i.test(html)
            const hasJavaScript = /<script[\s\S]*?<\/script>/i.test(html) || /onclick\s*=/i.test(html) || /addEventListener/i.test(html)
            const hasInteractivity = /onclick|onchange|onsubmit|addEventListener|querySelector/i.test(html)
            
            const features = []
            if (hasCSS) features.push('🎨 Custom CSS styling')
            if (hasJavaScript) features.push('⚡ JavaScript functionality')
            if (hasInteractivity) features.push('🖱️ Interactive elements')
            if (features.length === 0) features.push('📄 Basic HTML structure')

            console.log('[HTML_CANVAS] Canvas created successfully:', { artifactId, title, features })

            // AI SDK v5 best practice: Return a simple string that the AI can naturally use
            // The AI model will incorporate this into its response automatically
            return `HTML Canvas "${title}" created successfully with features: ${features.join(', ')}.\n\nHere's the complete code:\n\n\`\`\`html\n${html}\n\`\`\``

          } catch (error) {
            console.error('[HTML_CANVAS] Error creating canvas:', error)
            
            // Return simple error message for AI to use
            return `Failed to create HTML Canvas: ${error instanceof Error ? error.message : 'Unknown error'}`
          }
        }
      })
    } : undefined
    
    console.log('[TOOLS DEBUG] 🎯 TOOL CONFIGURATION:', {
      toolsRequested: chatRequest.toolsEnabled !== false,
      modelSupportsTools: modelSupportsTools(modelSelection.model),
      toolsEnabled: shouldEnableTools,
      toolCount: tools ? Object.keys(tools).length : 0,
      toolNames: tools ? Object.keys(tools) : [],
      model: modelSelection.model
    })

    // ============================================================================
    // 10. Create Stream Configuration with All Sophisticated Features
    // ============================================================================
    console.log('[Chat] About to create stream config:', {
      model: modelSelection.model,
      messagesCount: processedMessages?.length || 0,
      hasAttachments: chatRequest.attachmentFiles?.length || 0,
      messagesType: typeof processedMessages,
      isArray: Array.isArray(processedMessages),
      toolsEnabled: !!tools,
      toolCount: tools ? Object.keys(tools).length : 0
    })

    const streamConfig = await createStreamConfig(
      modelSelection.model,
      processedMessages,
      chatRequest,
      {
        // Pass all integrated sophisticated features
        modelConfiguration: integratedFeatures.modelConfiguration,
        modelSelection: modelSelection,
        session: session,
        personalization: integratedFeatures.personalization,
        compactionMetadata: integratedFeatures.compactionResult.metadata,
        // Add our simple AI SDK v5 tools
        tools: tools,
        // Pass model restriction information if any
        modelRestriction: modelSelection.wasRestricted ? {
          wasRestricted: true,
          originalModel: chatRequest.manualModel,
          restrictionMessage: modelSelection.restrictionMessage,
          requiresUpgrade: modelSelection.requiresUpgrade,
          suggestedPlan: modelSelection.suggestedPlan
        } : undefined,
        // Handle stream completion with sophisticated features
        onFinish: async (result) => {
          // Log assistant message with sophisticated features metadata (enhanced for anonymous)
          if ((session?.user || authResult.anonymousSession) && result.text) {
            logAssistantMessage(
              session?.user?.id,
              conversationId,
              result.text,
              modelSelection.model,
              result.usage,
              result.reasoning,
              {
                modelConfiguration: integratedFeatures.modelConfiguration,
                personalization: integratedFeatures.personalization,
                compactionMetadata: integratedFeatures.compactionResult.metadata
              },
              authResult.anonymousSession
            )
            
            // Track usage (only for authenticated users)
            if (session?.user) {
              trackUsage(
                session.user.id,
                modelSelection.model,
                result.usage
              )
            }
            
            // Generate conversation title using the master coordinator
            coordinator.generateTitle({
              conversationId,
              messages: processedMessages,
              isNewConversation: !chatRequest.conversationId
            }).catch(error => {
              console.error('[Chat] Coordinated title generation failed:', error)
            })
          }
          
          // V5 Attachment System - Attachments are saved automatically during streaming
          // Non-blocking database operations handled by V5 system
        }
      }
    )
    
    // ============================================================================
    // 10. Stream Response with All Sophisticated Features
    // ============================================================================
    console.log(`[Chat] Starting sophisticated stream (${Date.now() - startTime}ms)`, {
      modelType: integratedFeatures.modelConfiguration.modelType,
      streamingMode: integratedFeatures.modelConfiguration.streamingMode,
      hasPersonalization: integratedFeatures.personalization.hasPersonalization,
      compactionRatio: integratedFeatures.compactionResult.metadata.compressionRatio,
      features: integratedFeatures.metadata.features,
      totalProcessingTime: `${integratedFeatures.metadata.totalProcessingTime}ms`
    })
    
    // Use the existing streaming system for all requests (with or without attachments)
    // The V5 attachment data will be processed by the streaming handler via streamConfig
    console.log('[Chat] Using unified streaming system', {
      hasAttachments: attachmentMetadata.hasAttachments,
      attachmentCount: attachmentMetadata.attachmentCount
    })
    
    const result = await streamChatResponse(streamConfig)
    
    // If model was restricted, inject a notification event at the start of the stream
    if (modelSelection.wasRestricted) {
      // Create a custom stream that includes the model restriction event
      // Note: v5 removed createDataStreamResponse, using manual stream creation
      
      // Create our custom response with model restriction event
      return new Response(
        new ReadableStream({
          async start(controller) {
            const encoder = new TextEncoder()
            
            // Send model restriction event first
            const restrictionEvent = {
              type: 'modelRestriction',
              originalModel: chatRequest.manualModel,
              fallbackModel: modelSelection.model,
              restrictionMessage: modelSelection.restrictionMessage,
              requiresUpgrade: modelSelection.requiresUpgrade,
              suggestedPlan: modelSelection.suggestedPlan
            }
            
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(restrictionEvent)}\n\n`))
            
            // Then pipe the original result stream
            const reader = result.textStream.getReader()
            
            try {
              while (true) {
                const { done, value } = await reader.read()
                if (done) break
                controller.enqueue(encoder.encode(value))
              }
            } finally {
              controller.close()
            }
          }
        }),
        {
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            ...(authResult.anonymousSessionCookieHeader && {
              'Set-Cookie': authResult.anonymousSessionCookieHeader
            })
          }
        }
      )
    }
    
    // Use AI SDK v5 toUIMessageStreamResponse for proper reasoning support
    console.log('[Chat] Creating AI SDK v5 UI message stream response with reasoning support')
    
    // Create custom router data injection for the UI message stream
    const routerData = {
      type: 'router',
      conversationId,
      decision: {
        model: modelSelection.model,
        selectedModel: modelSelection.model,
        modelId: modelSelection.model,
        recommendedModels: [{
          modelId: modelSelection.model,
          reasoning: modelSelection.routerReasoning
        }],
        reasoning: modelSelection.routerReasoning,
        category: modelSelection.category,
        complexity: modelSelection.complexity,
        analysisResult: modelSelection.analysisResult ? {
          category: modelSelection.analysisResult.category,
          complexity: modelSelection.analysisResult.complexity,
          requirements: modelSelection.analysisResult.requirements
          // Exclude potentially large fields like full analysis text
        } : null,
        metadata: {
          routerLatency: Date.now() - startTime,
          manuallySelected: modelSelection.isManual
        },
        webSearchPerformed: searchResult.webSearchPerformed,
        webSearchResults: searchResult.webSearchResults,
        webSearchResultsData: searchResult.webSearchResultsData?.map(r => ({
          id: r.id,
          title: r.title,
          url: r.url,
          snippet: r.snippet?.substring(0, 200) // Limit snippet to 200 chars
          // Exclude massive metadata.markdown field
        })) || [],
        webSearchQueries: searchResult.webSearchQueries,
        webSearchTime: searchResult.webSearchTime,
        wasRestricted: modelSelection.wasRestricted
      }
    }
    
    // Determine if this model supports reasoning to avoid "Unhandled chunk type: reasoning" errors
    const supportsReasoning = isReasoningModel(modelSelection.model)
    console.log('[Chat] Model reasoning support check:', {
      modelId: modelSelection.model,
      supportsReasoning
    })
    
    // GROQ FIX: Groq models don't support reasoning chunks the same way as OpenAI
    // Using sendReasoning: true causes "Unhandled chunk type: reasoning" errors
    // Solution: Disable sendReasoning for Groq models and use proper streaming headers
    const isGroqModel = modelSelection.model.includes('groq/')
    const isKimiK2 = modelSelection.model.includes('kimi-k2')
    if (isGroqModel) {
      console.log('[Chat] Configuring Groq model streaming with sendReasoning: false to avoid chunk type errors', {
        modelId: modelSelection.model,
        isKimiK2: isKimiK2
      })
    }

    // Calculate final sendReasoning value
    const finalSendReasoning = supportsReasoning && !isGroqModel && !isKimiK2
    
    console.log('[Chat] Final UI streaming configuration:', {
      modelId: modelSelection.model,
      supportsReasoning,
      isGroqModel,
      isKimiK2,
      finalSendReasoning,
      fullModelPath: modelSelection.model
    })
    
    console.log('[Chat] About to call toUIMessageStreamResponse with sendReasoning:', finalSendReasoning)

    // KIMI K2 TEST: Try using toUIMessageStreamResponse with canary versions
    if (isKimiK2) {
      console.log('[Chat] Testing Kimi K2 with toUIMessageStreamResponse (canary versions)')
      // Continue to toUIMessageStreamResponse below instead of early return
    }

    // Check for attachment-heavy requests that might need special handling
    const hasLargeAttachments = attachmentMetadata.hasAttachments && attachmentMetadata.totalSize > 5 * 1024 * 1024 // 5MB
    const hasPDFAttachments = attachmentMetadata.attachmentTypes.includes('application/pdf')
    const maxAttachmentSize = 50 * 1024 * 1024 // 50MB limit for streaming safety
    
    // Validate attachment size before streaming to prevent stream failures
    if (attachmentMetadata.hasAttachments && attachmentMetadata.totalSize > maxAttachmentSize) {
      console.error('[Chat] Attachment too large for streaming:', {
        totalSize: `${(attachmentMetadata.totalSize / 1024 / 1024).toFixed(2)}MB`,
        maxSize: `${maxAttachmentSize / 1024 / 1024}MB`,
        attachmentTypes: attachmentMetadata.attachmentTypes
      })
      
      return NextResponse.json({
        error: 'Attachments too large',
        details: `Total attachment size (${(attachmentMetadata.totalSize / 1024 / 1024).toFixed(2)}MB) exceeds the ${maxAttachmentSize / 1024 / 1024}MB limit. Please use smaller files.`,
        maxSizeAllowed: `${maxAttachmentSize / 1024 / 1024}MB`,
        currentSize: `${(attachmentMetadata.totalSize / 1024 / 1024).toFixed(2)}MB`
      }, { 
        status: 413, // Payload Too Large
        headers: {
          ...(authResult.anonymousSessionCookieHeader && {
            'Set-Cookie': authResult.anonymousSessionCookieHeader
          })
        }
      })
    }
    
    if (hasLargeAttachments || hasPDFAttachments) {
      console.log('[Chat] Large/PDF attachments detected - using enhanced streaming configuration', {
        totalSize: `${(attachmentMetadata.totalSize / 1024 / 1024).toFixed(2)}MB`,
        hasPDF: hasPDFAttachments,
        attachmentCount: attachmentMetadata.attachmentCount,
        withinLimits: attachmentMetadata.totalSize <= maxAttachmentSize
      })
    }

    // Enhanced error handler for attachment streaming issues
    const handleStreamingError = (error: unknown): string => {
      console.error('[Chat] Streaming error occurred:', error)
      
      // Handle PDF-specific streaming errors
      if (hasPDFAttachments && error instanceof Error) {
        if (error.message.includes('pipe') || error.message.includes('stream')) {
          return 'Failed to process PDF attachment in stream. Please try uploading a smaller PDF file or contact support.'
        }
        if (error.message.includes('size') || error.message.includes('limit')) {
          return 'PDF attachment is too large for streaming. Please try a smaller file.'
        }
      }
      
      // Handle general streaming errors
      if (error instanceof Error) {
        if (error.message.includes('pipe') || error.message.includes('response')) {
          return 'Streaming response failed. Please try again or contact support if the issue persists.'
        }
        if (error.message.includes('timeout')) {
          return 'Request timed out. Please try again with a shorter message or smaller attachments.'
        }
        return `Stream error: ${error.message}`
      }
      
      if (typeof error === 'string') {
        return error
      }
      
      return 'An unknown streaming error occurred. Please try again.'
    }

    // Use AI SDK v5 toUIMessageStreamResponse with enhanced attachment handling
    return result.toUIMessageStreamResponse({
      sendReasoning: finalSendReasoning,  // Disable reasoning for Groq models and specifically Kimi K2 to avoid "Unhandled chunk type" errors
      sendSources: true,                                 // Enable sources if available
      // Note: getErrorMessage removed in v5, error handling done via try/catch
      // Note: onError removed in v5, error handling done via try/catch
      onFinish: async ({ messages }) => {
        console.log('[Chat] UI Message stream finished with', messages.length, 'messages')
        
        // Process any reasoning content from final messages
        for (const message of messages) {
          // In v5, messages have parts instead of content
          if (message.parts && message.parts.length > 0) {
            const textParts = message.parts.filter(p => p.type === 'text')
            console.log('[Chat] Final message has', textParts.length, 'text parts')
          }
          
          // Note: experimental_reasoning removed in v5, reasoning is handled differently
        }
      },
      headers: {
        // Essential headers for proper streaming compatibility with attachments
        'Transfer-Encoding': 'chunked',
        'Connection': 'keep-alive',
        'Content-Encoding': 'none', // Prevent proxy compression that could break streaming
        'Cache-Control': hasLargeAttachments ? 'no-cache, no-store, must-revalidate' : 'no-cache',
        'X-Router-Data': safeJSONStringify(routerData), // Safe unicode handling
        // Add tool execution summary to headers for chat interface visibility
        ...((result as any).toolExecutionSummary && {
          'X-Tool-Executions': safeJSONStringify((result as any).toolExecutionSummary)
        }),
        // Prevent content length calculation for large attachments
        ...(hasLargeAttachments && { 'Content-Length': undefined }),
        ...(authResult.anonymousSessionCookieHeader && {
          'Set-Cookie': authResult.anonymousSessionCookieHeader
        })
      }
    })
    
  } catch (error) {
    const elapsed = Date.now() - startTime
    
    // ============================================================================
    // Error Handling
    // ============================================================================
    
    // Handle rate limit errors specially
    if (error instanceof RateLimitError) {
      console.log(`[Chat] Rate limit exceeded (${elapsed}ms)`)
      
      return NextResponse.json({
        error: error.message,
        messagesRemaining: error.messagesRemaining,
        messagesUsed: error.messagesUsed,
        totalLimit: error.totalLimit,
        resetAt: error.resetAt,
        userPlan: error.userPlan,
        isAnonymous: error.isAnonymous,
        type: 'rate_limit_exceeded'
      }, {
        status: 429,
        headers: error.cookieHeader ? { 'Set-Cookie': error.cookieHeader } : {}
      })
    }
    
    // Log all other errors
    console.error(`[Chat] Error after ${elapsed}ms:`, error)
    apiLogger.error('Chat API error:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      elapsed,
    })
    
    // Return generic error response
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { 
      status: 500 
    })
  }
}

/**
 * OPTIONS handler for CORS
 */
export async function OPTIONS(request: NextRequest) {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}


